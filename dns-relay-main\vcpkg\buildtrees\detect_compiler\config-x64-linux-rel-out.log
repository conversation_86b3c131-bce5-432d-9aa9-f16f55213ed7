-- The C compiler identification is GNU 13.3.0
-- Detecting C compiler <PERSON><PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- The CXX compiler identification is GNU 13.3.0
-- Detecting CXX compiler <PERSON><PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done (21.5s)
-- Generating done (0.4s)
-- Build files have been written to: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/detect_compiler/x64-linux-rel
