#!/bin/bash

# DNS中继服务器dig测试脚本
# 用于测试运行在端口5353的DNS服务器

echo "=== DNS中继服务器 dig 测试 ==="
echo ""

SERVER="127.0.0.1"
PORT="5353"

# 测试函数
test_domain() {
    local domain=$1
    local description=$2
    
    echo "--- 测试 $description ---"
    echo "查询域名: $domain"
    echo "DNS服务器: $SERVER:$PORT"
    
    # 使用dig查询，设置较短的超时时间
    result=$(timeout 10 dig @$SERVER -p $PORT +time=3 +tries=2 $domain 2>&1)
    
    if echo "$result" | grep -q "ANSWER SECTION"; then
        echo "✓ 查询成功"
        echo "$result" | grep -A 10 "ANSWER SECTION" | head -5
    elif echo "$result" | grep -q "NXDOMAIN"; then
        echo "✓ 域名不存在 (NXDOMAIN) - 黑名单拦截成功"
    elif echo "$result" | grep -q "timed out\|no servers could be reached"; then
        echo "✗ 连接超时或无法到达服务器"
        echo "可能的原因："
        echo "  1. DNS服务器未在端口$PORT上运行"
        echo "  2. 防火墙阻止了连接"
        echo "  3. WSL网络配置问题"
    else
        echo "? 未知响应"
        echo "$result" | head -3
    fi
    
    echo "----------------------------------------"
    echo ""
}

# 检查dig是否安装
if ! command -v dig &> /dev/null; then
    echo "错误: dig命令未找到"
    echo "请安装dig: sudo apt-get install dnsutils"
    exit 1
fi

# 检查DNS服务器是否在运行
echo "检查DNS服务器状态..."
if netstat -ln 2>/dev/null | grep -q ":$PORT "; then
    echo "✓ 检测到端口$PORT上有服务在运行"
else
    echo "⚠ 警告: 未检测到端口$PORT上的服务"
    echo "请确保DNS服务器正在运行: ./dns_relay -p $PORT -s 8.8.8.8 -d"
fi
echo ""

# 执行测试
test_domain "baidu.com" "正常域名"
test_domain "2qq.cn" "黑名单域名"
test_domain "test0" "黑名单域名"
test_domain "bupt" "映射域名"
test_domain "google.com" "正常域名"

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "- ✓ 表示测试通过"
echo "- ✗ 表示测试失败"
echo "- ? 表示结果不确定"
echo ""
echo "如果所有dig测试都超时，可能是WSL环境的网络问题。"
echo "建议使用Python测试脚本: python3 test_dns.py"
