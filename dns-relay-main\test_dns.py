#!/usr/bin/env python3
"""
DNS中继服务器测试脚本
用于测试运行在非标准端口的DNS服务器
"""

import socket
import struct
import sys

def create_dns_query(domain, query_type=1):
    """创建DNS查询包"""
    # DNS头部
    transaction_id = 0x1234
    flags = 0x0100  # 标准查询
    questions = 1
    answer_rrs = 0
    authority_rrs = 0
    additional_rrs = 0
    
    header = struct.pack('!HHHHHH', transaction_id, flags, questions, 
                        answer_rrs, authority_rrs, additional_rrs)
    
    # 构建查询部分
    query = b''
    for part in domain.split('.'):
        query += struct.pack('!B', len(part)) + part.encode()
    query += b'\x00'  # 结束标志
    query += struct.pack('!HH', query_type, 1)  # 类型和类
    
    return header + query

def parse_dns_response(data):
    """解析DNS响应"""
    if len(data) < 12:
        return "响应数据太短"
    
    # 解析头部
    header = struct.unpack('!HHHHHH', data[:12])
    transaction_id, flags, questions, answers, authority, additional = header
    
    # 检查响应码
    rcode = flags & 0x000F
    if rcode == 0:
        status = "成功"
    elif rcode == 3:
        status = "域名不存在 (NXDOMAIN)"
    else:
        status = f"错误码: {rcode}"
    
    return f"响应状态: {status}, 答案数量: {answers}"

def test_dns_server(server_ip, server_port, domain):
    """测试DNS服务器"""
    try:
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)  # 5秒超时
        
        # 创建DNS查询
        query = create_dns_query(domain)
        
        # 发送查询
        print(f"查询域名: {domain}")
        print(f"DNS服务器: {server_ip}:{server_port}")
        sock.sendto(query, (server_ip, server_port))
        
        # 接收响应
        response, addr = sock.recvfrom(512)
        result = parse_dns_response(response)
        print(f"结果: {result}")
        
        sock.close()
        return True
        
    except socket.timeout:
        print("错误: 查询超时")
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False

def main():
    """主函数"""
    server_ip = "127.0.0.1"
    server_port = 5353
    
    print("=== DNS中继服务器测试 ===\n")
    
    # 测试域名列表
    test_domains = [
        ("baidu.com", "正常域名"),
        ("2qq.cn", "黑名单域名"),
        ("test0", "黑名单域名"),
        ("bupt", "映射域名"),
        ("google.com", "正常域名")
    ]
    
    success_count = 0
    total_count = len(test_domains)
    
    for domain, description in test_domains:
        print(f"\n--- 测试 {description} ---")
        if test_dns_server(server_ip, server_port, domain):
            success_count += 1
        print("-" * 40)
    
    print(f"\n=== 测试完成 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == 0:
        print("\n提示:")
        print("1. 确保DNS中继服务器正在运行: ./dns_relay -p 5353 -d")
        print("2. 检查防火墙设置")
        print("3. 确认服务器监听在127.0.0.1:5353")

if __name__ == "__main__":
    main()
