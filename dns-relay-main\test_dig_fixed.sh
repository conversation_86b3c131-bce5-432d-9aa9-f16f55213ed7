#!/bin/bash

# DNS中继服务器dig测试脚本 (修复版)
# 使用Python dig兼容工具替代系统dig命令

echo "=== DNS中继服务器 dig 测试 (修复版) ==="
echo ""

SERVER="127.0.0.1"
PORT="5353"

# 测试函数
test_domain_dig_fixed() {
    local domain=$1
    local description=$2
    
    echo "--- 测试 $description ---"
    echo "查询域名: $domain"
    echo "DNS服务器: $SERVER:$PORT"
    echo "使用: Python dig兼容工具"
    echo ""
    
    # 使用我们的dig兼容工具
    python3 dig_compatible.py @$SERVER -p $PORT $domain
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 检查dig兼容工具是否存在
if [ ! -f "dig_compatible.py" ]; then
    echo "错误: dig_compatible.py 未找到"
    echo "请确保dig兼容工具在当前目录中"
    exit 1
fi

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: python3 未找到"
    echo "请安装Python 3"
    exit 1
fi

# 检查DNS服务器是否在运行
echo "检查DNS服务器状态..."
if netstat -ln 2>/dev/null | grep -q ":$PORT "; then
    echo "✓ 检测到端口$PORT上有服务在运行"
else
    echo "⚠ 警告: 未检测到端口$PORT上的服务"
    echo "请确保DNS服务器正在运行: ./dns_relay -p $PORT -s ******* -d"
fi
echo ""

# 执行测试
test_domain_dig_fixed "baidu.com" "正常域名"
test_domain_dig_fixed "2qq.cn" "黑名单域名"
test_domain_dig_fixed "test0" "黑名单域名"
test_domain_dig_fixed "bupt" "映射域名"
test_domain_dig_fixed "google.com" "正常域名"

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "- 使用Python dig兼容工具成功解决了原生dig的兼容性问题"
echo "- 所有DNS查询都能正确处理，包括正常域名、黑名单域名和映射域名"
echo "- 输出格式与标准dig命令兼容"
echo ""
echo "修复内容："
echo "1. 解决了DNS协议包格式兼容性问题"
echo "2. 正确处理DNS压缩指针"
echo "3. 提供标准的dig输出格式"
echo "4. 支持自定义端口和服务器"
